import React, { useState, useEffect, useRef } from 'react';
import { QRCodeSVG } from 'qrcode.react'; // Fix the import

// Add CSS for range slider styling
const sliderCSS = `
  input[type="range"]::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: #2D8C88;
    cursor: pointer;
    box-shadow: 0 2px 6px rgba(45, 140, 136, 0.3);
  }

  input[type="range"]::-moz-range-thumb {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: #2D8C88;
    cursor: pointer;
    border: none;
    box-shadow: 0 2px 6px rgba(45, 140, 136, 0.3);
  }

  input[type="range"]::-webkit-slider-track {
    background: #e0e0e0;
    height: 6px;
    border-radius: 3px;
  }

  input[type="range"]::-moz-range-track {
    background: #e0e0e0;
    height: 6px;
    border-radius: 3px;
    border: none;
  }

  input[type="range"]:focus {
    outline: none;
  }

  input[type="range"]:focus::-webkit-slider-thumb {
    box-shadow: 0 0 0 3px rgba(45, 140, 136, 0.2);
  }

  input[type="range"]:focus::-moz-range-thumb {
    box-shadow: 0 0 0 3px rgba(45, 140, 136, 0.2);
  }

  /* Switch styles */
  .switch-container {
    position: relative;
    display: inline-block;
    width: 60px;
    height: 34px;
  }

  .switch-container input {
    opacity: 0;
    width: 0;
    height: 0;
  }

  .switch-slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(255, 255, 255, 0.2);
    transition: .4s;
    border-radius: 34px;
    border: 2px solid rgba(255, 255, 255, 0.3);
  }

  .switch-slider:before {
    position: absolute;
    content: "";
    height: 26px;
    width: 26px;
    left: 2px;
    bottom: 2px;
    background-color: white;
    transition: .4s;
    border-radius: 50%;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  }

  input:checked + .switch-slider {
    background-color: #2D8C88;
  }

  input:checked + .switch-slider:before {
    transform: translateX(26px);
  }

  input:disabled + .switch-slider {
    opacity: 0.5;
    cursor: not-allowed;
  }

  .switch-label {
    font-size: 12px;
    font-weight: 700;
    color: white;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.7);
    margin-bottom: 8px;
    letter-spacing: 0.5px;
  }
`;

// Inject CSS (only once)
if (typeof document !== 'undefined' && !document.getElementById('wrist-size-slider-styles')) {
  const styleElement = document.createElement('style');
  styleElement.id = 'wrist-size-slider-styles';
  styleElement.textContent = sliderCSS;
  document.head.appendChild(styleElement);
}

const Tryon = ({ onBackToHome }) => {
  // Refs for DOM elements
  const videoRef = useRef(null);
  const capturedImageRef = useRef(null);
  const canvasRef = useRef(null);

  // State variables
  const [isCaptured, setIsCaptured] = useState(false);
  const [selectedProduct, setSelectedProduct] = useState(null);
  const [isRightHand, setIsRightHand] = useState(false);
  const [showProductSelection, setShowProductSelection] = useState(false);
  const [activeTab, setActiveTab] = useState('Watches');
  const [showHandGuide, setShowHandGuide] = useState(true);
  const [isMobile, setIsMobile] = useState(false);
  const [isDesktop, setIsDesktop] = useState(false);

  // Watch size customization state
  const [userGender, setUserGender] = useState('men'); // 'men' or 'women'
  const [userWristSize, setUserWristSize] = useState(50); // Default men's wrist size in mm
  const [showWristSizeModal, setShowWristSizeModal] = useState(false); // Mobile-friendly modal

  // Autocapture state variables
  const [isAutoCaptureEnabled, setIsAutoCaptureEnabled] = useState(false);
  const [countdown, setCountdown] = useState(0);
  const [isHandInPosition, setIsHandInPosition] = useState(false);
  const [isCountdownActive, setIsCountdownActive] = useState(false);

  // Add new state for panel position
  const [panelPosition, setPanelPosition] = useState(0);
  const [isDragging, setIsDragging] = useState(false);
  const [startY, setStartY] = useState(0);
  const panelRef = useRef(null);

  // Detect mobile device and set viewport height for Chrome mobile
  useEffect(() => {
    const checkDevice = () => {
      const isMobileDevice = window.innerWidth <= 768;
      setIsMobile(isMobileDevice);
      setIsDesktop(!isMobileDevice);
    };

    // Fix viewport height for Chrome mobile
    const setVH = () => {
      const vh = window.innerHeight * 0.01;
      document.documentElement.style.setProperty('--vh', `${vh}px`);
    };

    checkDevice();
    setVH();

    window.addEventListener('resize', () => {
      checkDevice();
      setVH();
    });

    window.addEventListener('orientationchange', () => {
      setTimeout(() => {
        setVH();
      }, 100);
    });

    return () => {
      window.removeEventListener('resize', checkDevice);
      window.removeEventListener('orientationchange', setVH);
    };
  }, []);

  // Realistic sizing configuration
  // Average adult wrist circumference: 165mm (men), 155mm (women)
  // Average wrist width when viewed from top: ~55-65mm
  // SVG guide circle represents the wrist area (120px diameter in 800px viewBox)
  // This means the circle represents approximately 60mm in real life

  // Default wrist sizes by gender (top view width in mm)
  const DEFAULT_WRIST_SIZES = {
    men: 50,    // mm - average men's wrist width from top view
    women: 45   // mm - average women's wrist width from top view
  };

  const SVG_WRIST_CIRCLE_DIAMETER = 120; // SVG circle diameter in viewBox units
  const SVG_VIEWBOX_WIDTH = 800; // SVG viewBox width
  const SVG_VIEWBOX_HEIGHT = 600; // SVG viewBox height

  // Size configuration - Increased for better visibility
  const WATCH_WIDTH = 25; // percentage of container width
  const BRACELET_WIDTH = 15; // percentage of container width
  const WATCH_HEIGHT = 38; // percentage of container height
  const BRACELET_HEIGHT = 55; // percentage of container height - INCREASED for better visibility

  // Test: Try changing WATCH_HEIGHT to 50 to see a much taller watch
  // Test: Try changing WATCH_HEIGHT to 15 to see a shorter watch
  // Test: Try changing BRACELET_HEIGHT to 70 to see a much taller bracelet
  // Test: Try changing BRACELET_HEIGHT to 20 to see a shorter bracelet
  // Note: Scale is now dynamic - smaller height = smaller scale, larger height = larger scale

  // Calculate realistic watch sizing based on actual dimensions and user's wrist size
  const calculateWatchDimensions = (watch, containerWidth, containerHeight) => {
    // Get the default wrist size for the current gender
    const defaultWristSize = DEFAULT_WRIST_SIZES[userGender];

    // Calculate INVERSE scaling factor - smaller wrist = larger watch, larger wrist = smaller watch
    const inverseWristSizeRatio = defaultWristSize / userWristSize;

    // Calculate the scale factor from real world to SVG coordinates using user's wrist size
    const mmToSvgScale = SVG_WRIST_CIRCLE_DIAMETER / userWristSize;

    // Convert watch dimensions to SVG units
    const watchWidthSvg = watch.totalWidth * mmToSvgScale;
    const watchHeightSvg = watch.totalHeight * mmToSvgScale;
    const dialDiameterSvg = watch.dialDiameter * mmToSvgScale;

    // Convert SVG units to container percentages
    const watchWidthPercent = (watchWidthSvg / SVG_VIEWBOX_WIDTH) * 100;
    const watchHeightPercent = (watchHeightSvg / SVG_VIEWBOX_HEIGHT) * 100;
    const dialDiameterPercent = (dialDiameterSvg / SVG_VIEWBOX_WIDTH) * 100;

    // Calculate positioning - watches should be positioned on the wrist circle
    // SVG circle is at cx="400" cy="300" (center of the hand area)
    const positionX = 50; // Center horizontally (400/800 = 50%)
    const positionY = 50; // Center vertically (300/600 = 50%)

    return {
      width: Math.max(watchWidthPercent, 8), // Minimum 8% for visibility
      height: Math.max(watchHeightPercent, 10), // Minimum 10% for visibility
      dialDiameter: dialDiameterPercent,
      positionX,
      positionY,
      scale: Math.min(watchWidthPercent / 15, watchHeightPercent / 20) * inverseWristSizeRatio, // Dynamic scale with INVERSE wrist size adjustment
      realWidth: watch.totalWidth,
      realHeight: watch.totalHeight,
      caseDiameter: watch.caseDiameter,
      wristSizeRatio: inverseWristSizeRatio // Include inverse ratio for additional scaling if needed
    };
  };

  // Calculate watch position based on hand orientation and anatomy
  const getWatchPosition = (watchData, isRightHand) => {
    const baseDimensions = calculateWatchDimensions(watchData, 400, 600);

    // Adjust position based on hand orientation
    // Watches are typically worn on the top of the wrist
    let adjustedX = baseDimensions.positionX;
    let adjustedY = baseDimensions.positionY - 2; // Slightly higher on the wrist

    // For right hand, watch might be positioned slightly differently
    if (isRightHand) {
      adjustedX = baseDimensions.positionX + 1; // Slight adjustment for right hand
    }

    // Calculate scale to match SVG shape height
    const svgHeight = 300; // Height of the wrist/forearm area in SVG
    const watchHeight = watchData.totalHeight;
    const scaleToFitHeight = svgHeight / watchHeight;

    // Adjust for different watch types
    switch (watchData.type) {
      case 'smartwatch':
        // Smart watches are often worn higher on the wrist
        adjustedY -= 1;
        break;
      case 'luxury':
        // Luxury watches might be positioned more precisely
        adjustedY -= 0.5;
        break;
      case 'sport':
        // Sport watches might be worn slightly looser
        adjustedY += 0.5;
        break;
      default:
        break;
    }

    return {
      ...baseDimensions,
      positionX: adjustedX,
      positionY: adjustedY,
      scale: Math.min(baseDimensions.scale, scaleToFitHeight) // Ensure watch doesn't exceed SVG height
    };
  };

  // Realistic watch data with actual dimensions (in millimeters)
  const watches = [
    {
      name: "Classic Black",
      path: "watches/watch_1.png",
      // Rolex Submariner style - 40mm case
      caseDiameter: 41, // mm
      caseThickness: 12.5, // mm
      totalWidth: 42, // mm (including crown)
      totalHeight: 47, // mm (lug to lug)
      dialDiameter: 31, // mm (visible dial)
      type: "dress",
      dialSize: 40
    },
    {
      name: "Silver Chrono",
      path: "watches/watch_2.png",
      // Omega Speedmaster style - 42mm case
      caseDiameter: 42, // mm
      caseThickness: 13.2, // mm
      totalWidth: 44, // mm
      totalHeight: 48.5, // mm
      dialDiameter: 33, // mm
      type: "sport",
      dialSize: 42
    },
    {
      name: "Gold Luxury",
      path: "watches/watch_3.png",
      // Patek Philippe Calatrava style - 38mm case
      caseDiameter: 39, // mm
      caseThickness: 8.5, // mm
      totalWidth: 39, // mm
      totalHeight: 45, // mm
      dialDiameter: 30, // mm
      type: "luxury",
      dialSize: 38
    },
    {
      name: "Sport Blue",
      path: "watches/watch_6.png",
      // Apple Watch style - 44mm case
      caseDiameter: 41, // mm (width)
      caseThickness: 10.7, // mm
      totalWidth: 44, // mm
      totalHeight: 38, // mm (height - rectangular)
      dialDiameter: 35, // mm (screen diagonal)
      type: "smartwatch",
      dialSize: 44
    },
    {
      name: "Minimalist",
      path: "watches/watch_5.png",
      // Daniel Wellington style - 36mm case
      caseDiameter: 36, // mm
      caseThickness: 6, // mm
      totalWidth: 37, // mm
      totalHeight: 43, // mm
      dialDiameter: 28, // mm
      type: "minimalist",
      dialSize: 36
    },
    {
      name: "Rose Gold",
      path: "watches/watch_4.png",
      // Michael Kors style - 39mm case
      caseDiameter: 44, // mm
      caseThickness: 11, // mm
      totalWidth: 41, // mm
      totalHeight: 46, // mm
      dialDiameter: 31, // mm
      type: "fashion",
      dialSize: 41
    }
  ];

  const bracelets = [
    { name: "Silver Chain", path: "bracelets/bracelet_1.png" },
    { name: "Gold Bangle", path: "bracelets/bracelet_2.png" },
    { name: "Leather Wrap", path: "bracelets/bracelet_3.png" },
    { name: "Diamond Tennis", path: "bracelets/bracelet_4.png" },
    { name: "Beaded Stone", path: "bracelets/bracelet_5.png" },
    { name: "Charm Bracelet", path: "bracelets/bracelet_6.png" }
  ];

  // Initialize camera
  const initCamera = async () => {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({
        video: {
          facingMode: 'environment',
          width: { ideal: 1920 },
          height: { ideal: 1080 }
        }
      });
      if (videoRef.current) {
        videoRef.current.srcObject = stream;
      }
    } catch (err) {
      console.error("Error accessing camera:", err);
      // Demo mode fallback
      if (capturedImageRef.current) {
        capturedImageRef.current.src = "sample-hand.jpg";
        capturedImageRef.current.style.display = "block";
      }
      console.log("Camera not available - demo mode");
      setIsCaptured(true);
      setShowProductSelection(true);
    }
  };

  // Capture current frame
  const captureFrame = () => {
    if (!videoRef.current || !canvasRef.current) return null;

    const canvas = canvasRef.current;
    const video = videoRef.current;
    canvas.width = video.videoWidth;
    canvas.height = video.videoHeight;
    const ctx = canvas.getContext('2d');
    ctx.drawImage(video, 0, 0, canvas.width, canvas.height);
    return canvas.toDataURL('image/png');
  };

  // Improved background remover that preserves light colors
  const removeBackground = (imgElement, productType = 'watch') => {
    const img = new Image();
    img.crossOrigin = 'anonymous';

    img.onload = function() {
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');
      canvas.width = img.naturalWidth;
      canvas.height = img.naturalHeight;
      ctx.drawImage(img, 0, 0);

      try {
        const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
        const data = imageData.data;
        const width = canvas.width;
        const height = canvas.height;

        // First pass: Identify edge pixels to preserve product boundaries
        const edgePixels = new Set();
        for (let y = 1; y < height - 1; y++) {
          for (let x = 1; x < width - 1; x++) {
            const idx = (y * width + x) * 4;
            const r = data[idx];
            const g = data[idx + 1];
            const b = data[idx + 2];

            // Check surrounding pixels for edge detection
            let isEdge = false;
            for (let dy = -1; dy <= 1; dy++) {
              for (let dx = -1; dx <= 1; dx++) {
                if (dx === 0 && dy === 0) continue;
                const neighborIdx = ((y + dy) * width + (x + dx)) * 4;
                const nr = data[neighborIdx];
                const ng = data[neighborIdx + 1];
                const nb = data[neighborIdx + 2];

                // If there's a significant color difference, it's an edge
                const colorDiff = Math.abs(r - nr) + Math.abs(g - ng) + Math.abs(b - nb);
                if (colorDiff > 60) {
                  isEdge = true;
                  break;
                }
              }
              if (isEdge) break;
            }

            if (isEdge) {
              edgePixels.add(idx / 4);
            }
          }
        }

        // Second pass: Remove only white background while preserving all product colors
        for (let i = 0; i < data.length; i += 4) {
          const r = data[i];
          const g = data[i + 1];
          const b = data[i + 2];
          const pixelIndex = i / 4;

          // Calculate color properties
          const brightness = (r + g + b) / 3;

          // Check if pixel is near edges (preserve product boundaries)
          const isNearEdge = edgePixels.has(pixelIndex);

          // Only remove pure white or near-white pixels that aren't edges
          const isPureWhite = (
            r > 245 &&
            g > 245 &&
            b > 245 &&
            Math.abs(r - g) < 10 &&
            Math.abs(g - b) < 10 &&
            !isNearEdge
          );

          // Remove background if it's pure white and not an edge
          if (isPureWhite) {
            data[i + 3] = 0; // Make fully transparent
          } else if (brightness > 240 && !isNearEdge) {
            // For very bright but not pure white, reduce opacity slightly
            data[i + 3] = Math.max(0, data[i + 3] - 50);
          }
        }

        ctx.putImageData(imageData, 0, 0);
        imgElement.src = canvas.toDataURL('image/png');

        // Apply product-specific styling that preserves all colors
        imgElement.style.filter = 'none';
        imgElement.style.mixBlendMode = 'normal';
        imgElement.style.opacity = '1';

      } catch (e) {
        console.warn('Canvas operation failed:', e);
        // Fallback styling that preserves all colors
        imgElement.style.filter = 'none';
        imgElement.style.mixBlendMode = 'normal';
        imgElement.style.opacity = '1';
      }
    };

    img.onerror = function() {
      console.warn('Image loading failed');
      // Fallback styling
      imgElement.style.filter = 'none';
      imgElement.style.mixBlendMode = 'normal';
      imgElement.style.opacity = '1';
    };

    img.src = imgElement.src;
  };

  // Placeholder for hand detection
  const detectHandOrientation = (imageData) => {
    // Simple heuristic for demo purposes
    return Math.random() > 0.5;
  };

  // Detect if arm and wrist are within the SVG guide area
  const detectHandInPosition = () => {
    if (!videoRef.current || !canvasRef.current) return false;

    const video = videoRef.current;
    const canvas = canvasRef.current;
    const ctx = canvas.getContext('2d');

    // Set canvas size to match video
    canvas.width = video.videoWidth;
    canvas.height = video.videoHeight;

    // Draw current video frame
    ctx.drawImage(video, 0, 0, canvas.width, canvas.height);

    // Get the video container dimensions to calculate the guide area
    const videoContainer = video.parentElement;
    const containerRect = videoContainer.getBoundingClientRect();

    // Calculate the actual video display area (accounting for object-fit: cover)
    const videoAspect = video.videoWidth / video.videoHeight;
    const containerAspect = containerRect.width / containerRect.height;

    let displayWidth, displayHeight, offsetX, offsetY;

    if (videoAspect > containerAspect) {
      // Video is wider - height fills container, width is cropped
      displayHeight = containerRect.height;
      displayWidth = displayHeight * videoAspect;
      offsetX = (displayWidth - containerRect.width) / 2;
      offsetY = 0;
    } else {
      // Video is taller - width fills container, height is cropped
      displayWidth = containerRect.width;
      displayHeight = displayWidth / videoAspect;
      offsetX = 0;
      offsetY = (displayHeight - containerRect.height) / 2;
    }

    // Calculate the guide areas in canvas coordinates
    // SVG viewBox is 800x600
    // Main rectangle: x="320" y="150" width="160" height="300" (wrist/forearm area)
    // Circle: cx="400" cy="300" r="60" (hand area)

    const scaleX = canvas.width / displayWidth;
    const scaleY = canvas.height / displayHeight;

    // Wrist/forearm area (rectangle)
    const rectX = Math.max(0, ((320 / 800) * displayWidth - offsetX) * scaleX);
    const rectY = Math.max(0, ((150 / 600) * displayHeight - offsetY) * scaleY);
    const rectWidth = Math.min(canvas.width - rectX, ((160 / 800) * displayWidth) * scaleX);
    const rectHeight = Math.min(canvas.height - rectY, ((300 / 600) * displayHeight) * scaleY);

    // Hand area (circle)
    const circleX = Math.max(0, ((340 / 800) * displayWidth - offsetX) * scaleX); // Adjusted for circle bounds
    const circleY = Math.max(0, ((240 / 600) * displayHeight - offsetY) * scaleY); // Adjusted for circle bounds
    const circleWidth = Math.min(canvas.width - circleX, ((120 / 800) * displayWidth) * scaleX); // Circle diameter
    const circleHeight = Math.min(canvas.height - circleY, ((120 / 600) * displayHeight) * scaleY); // Circle diameter

    try {
      // Check wrist/forearm area (rectangle)
      const rectImageData = ctx.getImageData(rectX, rectY, rectWidth, rectHeight);
      const rectData = rectImageData.data;

      // Check hand area (circle approximation)
      const circleImageData = ctx.getImageData(circleX, circleY, circleWidth, circleHeight);
      const circleData = circleImageData.data;

      let rectSkinPixels = 0;
      let rectTotalPixels = 0;
      let circleSkinPixels = 0;
      let circleTotalPixels = 0;

      // Enhanced skin tone detection
      const isSkinTone = (r, g, b) => {
        // Multiple skin tone ranges to cover different skin colors
        const condition1 = r > 95 && g > 40 && b > 20 && r > g && r > b && Math.abs(r - g) > 15;
        const condition2 = r > 220 && g > 210 && b > 170 && Math.abs(r - g) < 15 && Math.abs(g - b) < 15;
        const condition3 = r > 120 && g > 80 && b > 50 && r > g && g > b;
        const condition4 = r > 180 && g > 120 && b > 90 && r > g && g >= b;

        return condition1 || condition2 || condition3 || condition4;
      };

      // Analyze rectangle area (wrist/forearm)
      for (let i = 0; i < rectData.length; i += 4) {
        const r = rectData[i];
        const g = rectData[i + 1];
        const b = rectData[i + 2];

        if (isSkinTone(r, g, b)) {
          rectSkinPixels++;
        }
        rectTotalPixels++;
      }

      // Analyze circle area (hand)
      for (let i = 0; i < circleData.length; i += 4) {
        const r = circleData[i];
        const g = circleData[i + 1];
        const b = circleData[i + 2];

        if (isSkinTone(r, g, b)) {
          circleSkinPixels++;
        }
        circleTotalPixels++;
      }

      // Calculate skin ratios
      const rectSkinRatio = rectSkinPixels / rectTotalPixels;
      const circleSkinRatio = circleSkinPixels / circleTotalPixels;

      // Both wrist/forearm AND hand areas must have sufficient skin tone presence
      // Wrist area needs at least 20% skin tone (forearm/wrist)
      // Hand area needs at least 25% skin tone (hand/fingers)
      const wristInPosition = rectSkinRatio > 0.20;
      const handInPosition = circleSkinRatio > 0.25;

      return wristInPosition && handInPosition;

    } catch (error) {
      console.warn('Hand detection error:', error);
      return false;
    }
  };

  // Apply product to watch position with dynamic scaling
  const applyProductToWatchPosition = (productPath, productType) => {
    // Clear any existing product first
    setSelectedProduct(null);

    // Find the watch data for dial size
    const watchData = watches.find(w => w.path === productPath);

    // Small delay to ensure the old product is removed before adding new one
    setTimeout(() => {
      setSelectedProduct({
        path: productPath,
        dialSize: watchData?.dialSize || 40, // Default to 40mm if not found
        dimensions: watchData // Pass full watch dimensions for scaling
      });
    }, 50);
  };

  // Handle capture button click
  const handleCapture = () => {
    if (!isCaptured) {
      const capturedDataUrl = captureFrame();
      if (capturedImageRef.current && capturedDataUrl) {
        capturedImageRef.current.src = capturedDataUrl;
        capturedImageRef.current.style.display = 'block';
      }
      setIsCaptured(true);
      setShowProductSelection(true); // Show products immediately
      setShowHandGuide(false);

      // Detect hand orientation
      if (canvasRef.current && videoRef.current) {
        const canvas = canvasRef.current;
        const ctx = canvas.getContext('2d');
        canvas.width = videoRef.current.videoWidth;
        canvas.height = videoRef.current.videoHeight;
        ctx.drawImage(videoRef.current, 0, 0);
        const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
        setIsRightHand(detectHandOrientation(imageData));
      }
    } else {
      setShowProductSelection(true);
    }
  };

  // Handle back button click
  const handleBack = () => {
    if (capturedImageRef.current) {
      capturedImageRef.current.style.display = 'none';
    }
    setIsCaptured(false);
    setSelectedProduct(null);
    setIsRightHand(false);
    setShowProductSelection(false);
    setShowWristSizeModal(false);
    setShowHandGuide(true);
  };

  // Handle gender selection
  const handleGenderChange = (gender) => {
    setUserGender(gender);
    setUserWristSize(gender === 'men' ? 50 : 45); // Set initial size based on gender
  };

  // Handle wrist size change with Safari mobile fix
  const handleWristSizeChange = (size) => {
    setUserWristSize(size);

    // Force re-render on Safari mobile for large wrist sizes
    const isLargeWrist = (userGender === 'men' && size >= 50) || (userGender === 'women' && size >= 45);
    if (isLargeWrist && /^((?!chrome|android).)*safari/i.test(navigator.userAgent)) {
      // Force DOM update by briefly removing and re-adding the product
      const currentProduct = selectedProduct;
      setSelectedProduct(null);
      setTimeout(() => {
        setSelectedProduct(currentProduct);
      }, 10);
    }
  };

  // Handle continue to product selection - No longer needed since both panels show together
  // const handleContinueToProducts = () => {
  //   setShowWristSizeInput(false);
  //   setShowProductSelection(true);
  // };

  // Handle tab change
  const handleTabChange = (tabName) => {
    setActiveTab(tabName);
  };

  // Handle product selection
  const handleProductSelect = (product) => {
    applyProductToWatchPosition(product.path, activeTab);
  };

  // Initialize camera on component mount
  useEffect(() => {
    initCamera();
  }, []);

  // Hand position monitoring effect (only when autocapture is enabled)
  useEffect(() => {
    if (!isAutoCaptureEnabled || isCaptured) return;

    const interval = setInterval(() => {
      const handInPosition = detectHandInPosition();
      setIsHandInPosition(handInPosition);

      // Only start countdown if hand is properly positioned
      if (handInPosition && !isCountdownActive && countdown === 0) {
        setIsCountdownActive(true);
      }

      // Stop countdown if hand moves out of position
      if (!handInPosition && isCountdownActive) {
        setIsCountdownActive(false);
        setCountdown(0);
      }
    }, 150); // Check every 150ms for better performance

    return () => clearInterval(interval);
  }, [isAutoCaptureEnabled, isCaptured, isCountdownActive, countdown]);

  // Countdown effect (starts automatically when hand is in position)
  useEffect(() => {
    if (!isCountdownActive || isCaptured) {
      setCountdown(0);
      return;
    }

    // Only start countdown if hand is still in position
    if (!isHandInPosition) {
      setIsCountdownActive(false);
      setCountdown(0);
      return;
    }

    // Start countdown
    setCountdown(3);

    const countdownInterval = setInterval(() => {
      setCountdown(prev => {
        // Double-check hand position before continuing countdown
        if (!isHandInPosition) {
          clearInterval(countdownInterval);
          setIsCountdownActive(false);
          return 0;
        }

        if (prev <= 1) {
          // Countdown finished - trigger capture
          clearInterval(countdownInterval);
          setIsCountdownActive(false);
          setIsAutoCaptureEnabled(false); // Turn off autocapture after capture
          handleCapture();
          return 0;
        }
        return prev - 1;
      });
    }, 1000);

    return () => clearInterval(countdownInterval);
  }, [isCountdownActive, isCaptured, isHandInPosition]);

  // Handle autocapture switch toggle
  const handleAutoCaptureToggle = () => {
    const newState = !isAutoCaptureEnabled;
    setIsAutoCaptureEnabled(newState);

    // Reset states when turning off
    if (!newState) {
      setIsCountdownActive(false);
      setCountdown(0);
      setIsHandInPosition(false);
    }
  };

  // Reset autocapture when going back
  const handleBackWithReset = () => {
    setIsAutoCaptureEnabled(false);
    setIsCountdownActive(false);
    setCountdown(0);
    setIsHandInPosition(false);
    setShowWristSizeModal(false);
    setUserGender('men');
    setUserWristSize(50); // Default to men's size
    handleBack();
  };

  // Get current products based on active tab
  const getCurrentProducts = () => {
    return activeTab === 'Watches' ? watches : bracelets;
  };

  // Add touch gesture handlers
  const handleTouchStart = (e) => {
    setIsDragging(true);
    setStartY(e.touches[0].clientY);
  };

  const handleTouchMove = (e) => {
    if (!isDragging) return;
    
    const currentY = e.touches[0].clientY;
    const diff = currentY - startY;
    
    // Only allow dragging down
    if (diff > 0) {
      setPanelPosition(diff);
    }
  };

  const handleTouchEnd = () => {
    if (!isDragging) return;
    
    setIsDragging(false);
    
    // If dragged more than 100px down, close the panel
    if (panelPosition > 100) {
      setShowProductSelection(false);
    }
    
    // Reset position
    setPanelPosition(0);
  };

  // Add click outside handler for modals
  useEffect(() => {
    const handleClickOutside = (e) => {
      if (showWristSizeModal && !e.target.closest('.modal-content')) {
        setShowWristSizeModal(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    document.addEventListener('touchstart', handleClickOutside);
    
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
      document.removeEventListener('touchstart', handleClickOutside);
    };
  }, [showWristSizeModal]);

  // Desktop QR Code Component
  const DesktopQRCode = () => (
    <div style={styles.desktopContainer}>
      <div style={styles.qrContainer}>
        <h2 style={styles.qrTitle}>Scan QR Code to Try On</h2>
        <p style={styles.qrSubtitle}>Open this page on your mobile device to experience the virtual try-on feature</p>
        <div style={styles.qrWrapper}>
          <QRCodeSVG
            value="https://tryon-v1.vercel.app/"
            size={256}
            level="H"
            includeMargin={true}
          />
        </div>
        <p style={styles.qrLink}>https://tryon-v1.vercel.app/</p>
        <button
          style={styles.homeBtn}
          onClick={onBackToHome}
          aria-label="Home"
        >
          ← Back to Home
        </button>
      </div>
    </div>
  );

  // Return desktop view if not on mobile
  if (isDesktop) {
    return <DesktopQRCode />;
  }

  // Update product selection panel JSX
  return (
    <div style={styles.container}>
      <div style={styles.cameraContainer}>
        <video
          ref={videoRef}
          style={styles.cameraFeed}
          autoPlay
          playsInline
          muted
        />
        <canvas ref={canvasRef} style={{ display: 'none' }} />
        <img
          ref={capturedImageRef}
          style={styles.capturedImage}
          alt="Captured hand"
        />

        {/* Simple Home Button - Only visible when not captured */}
        {!isCaptured && (
          <button
            style={styles.homeBtn}
            onClick={onBackToHome}
            aria-label="Home"
          >
            ←
          </button>
        )}

        {/* Autocapture Switch Button - Only visible when not captured */}
        {!isCaptured && (
          <div style={{
            position: 'absolute',
            top: isMobile ? 10 : 20,
            right: isMobile ? 10 : 20,
            zIndex: 20,
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            gap: '8px',
            padding: '12px',
            backgroundColor: 'rgba(0, 0, 0, 0.5)',
            borderRadius: '20px',
            backdropFilter: 'blur(10px)',
            WebkitBackdropFilter: 'blur(10px)',
            boxShadow: '0 4px 12px rgba(0, 0, 0, 0.2)',
            border: '1px solid rgba(255, 255, 255, 0.1)'
          }}>
            <label className="switch-label">
              Auto Capture
            </label>
            <label className="switch-container">
              <input
                type="checkbox"
                checked={isAutoCaptureEnabled}
                onChange={handleAutoCaptureToggle}
                disabled={isCountdownActive}
                aria-label="Toggle auto capture"
              />
              <span className="switch-slider"></span>
            </label>
          </div>
        )}

        {/* Simple Back Button - Only visible when captured */}
        {isCaptured && (
          <button
            style={styles.backBtn}
            onClick={handleBackWithReset}
            aria-label="Back"
          >
            ←
          </button>
        )}

        {/* Countdown Display - Only visible during active countdown */}
        {isCountdownActive && (
          <div style={styles.countdownDisplay}>
            <div style={styles.countdownNumber}>{countdown}</div>
            <div style={styles.countdownText}>Auto capturing...</div>
          </div>
        )}

        {/* Status Messages */}
        {isAutoCaptureEnabled && !isHandInPosition && !isCountdownActive && (
          <div style={styles.statusMessage}>
            <div style={styles.statusText}>Position your arm and wrist in the guide area</div>
            <div style={styles.statusSubtext}>Countdown will start automatically when detected</div>
          </div>
        )}

        {isAutoCaptureEnabled && isHandInPosition && !isCountdownActive && (
          <div style={styles.statusMessage}>
            <div style={{...styles.statusText, backgroundColor: 'rgba(45, 140, 136, 0.9)'}}>
              Perfect! Starting countdown...
            </div>
          </div>
        )}

        {/* Hand Guide SVG */}
        {showHandGuide && (
          <div
            style={{
              ...styles.handGuide,
              opacity: isAutoCaptureEnabled && isHandInPosition ? 0.9 : 0.8,
              filter: isAutoCaptureEnabled && isHandInPosition
                ? 'drop-shadow(0 2px 8px rgba(45, 140, 136, 0.5))'
                : isAutoCaptureEnabled && !isHandInPosition
                  ? 'drop-shadow(0 2px 8px rgba(255, 107, 107, 0.5))'
                  : 'drop-shadow(0 2px 8px rgba(255, 255, 255, 0.3))'
            }}
            className={isMobile ? 'mobile-hand-guide' : ''}
            aria-hidden="true"
          >
            <svg viewBox="0 0 800 600" xmlns="http://www.w3.org/2000/svg">
              {/* Wrist guide lines */}
              <path
                d="M100 480 C 150 460, 200 450, 250 450 L 550 450 C 600 450, 650 460, 700 480"
                stroke={
                  isAutoCaptureEnabled && isHandInPosition
                    ? "#2D8C88"
                    : isAutoCaptureEnabled && !isHandInPosition
                      ? "#ff6b6b"
                      : "white"
                }
                strokeWidth="5"
                fill="none"
                strokeLinecap="round"
              />
              <path
                d="M100 120 C 150 140, 200 150, 250 150 L 550 150 C 600 150, 650 140, 700 120"
                stroke={
                  isAutoCaptureEnabled && isHandInPosition
                    ? "#2D8C88"
                    : isAutoCaptureEnabled && !isHandInPosition
                      ? "#ff6b6b"
                      : "white"
                }
                strokeWidth="5"
                fill="none"
                strokeLinecap="round"
              />
              {/* Wrist/Forearm area (rectangle) */}
              <rect
                x="320"
                y="150"
                width="160"
                height="300"
                fill={
                  isAutoCaptureEnabled && isHandInPosition
                    ? "#2D8C88"
                    : isAutoCaptureEnabled && !isHandInPosition
                      ? "#ff6b6b"
                      : "white"
                }
                opacity={isAutoCaptureEnabled && isHandInPosition ? "0.25" : "0.15"}
                rx="15"
                stroke={
                  isAutoCaptureEnabled && isHandInPosition
                    ? "#2D8C88"
                    : isAutoCaptureEnabled && !isHandInPosition
                      ? "#ff6b6b"
                      : "white"
                }
                strokeWidth="2"
              />
              {/* Hand area (circle) */}
              <circle
                cx="400"
                cy="300"
                r="60"
                fill={
                  isAutoCaptureEnabled && isHandInPosition
                    ? "#2D8C88"
                    : isAutoCaptureEnabled && !isHandInPosition
                      ? "#ff6b6b"
                      : "white"
                }
                opacity={isAutoCaptureEnabled && isHandInPosition ? "0.3" : "0.2"}
                stroke={
                  isAutoCaptureEnabled && isHandInPosition
                    ? "#2D8C88"
                    : isAutoCaptureEnabled && !isHandInPosition
                      ? "#ff6b6b"
                      : "white"
                }
                strokeWidth="2"
              />
              {/* Labels for clarity */}
              {isAutoCaptureEnabled && (
                <>
                  <text x="400" y="140" textAnchor="middle" fill="white" fontSize="14" fontWeight="bold">
                    WRIST & FOREARM
                  </text>
                  <text x="400" y="480" textAnchor="middle" fill="white" fontSize="14" fontWeight="bold">
                    HAND
                  </text>
                </>
              )}
            </svg>
          </div>
        )}

        {/* Product Display */}
        {selectedProduct && (
          <div style={{
            ...styles.productPosition,
            width: activeTab === 'Watches' ? `${WATCH_WIDTH}%` : `${BRACELET_WIDTH}%`,
            height: activeTab === 'Watches'
              ? (() => {
                  const defaultWristSize = DEFAULT_WRIST_SIZES[userGender];
                  let adjustedHeight = WATCH_HEIGHT;
                  const isLargeWrist = (userGender === 'men' && userWristSize >= 50) || (userGender === 'women' && userWristSize >= 45);

                  // Increase container height for larger wrists (≥ default size) to accommodate height compensation
                  if (userWristSize >= defaultWristSize) {
                    const sizeIncrease = (userWristSize - defaultWristSize) / defaultWristSize;
                    if (isLargeWrist) {
                      // For large wrists, allow MUCH more height increase to accommodate 100% scaling
                      adjustedHeight = WATCH_HEIGHT * (1 + sizeIncrease * 0.9); // Up to 80% container height increase
                    } else {
                      // For smaller wrists, use original 15% increase
                      adjustedHeight = WATCH_HEIGHT * (1 + sizeIncrease * 0.15);
                    }
                  }

                  return `${adjustedHeight}%`;
                })()
              : `${BRACELET_HEIGHT}%`,
            // Apply clipping only for smaller wrist sizes to prevent SVG height overflow
            // For large wrists (>= 50mm men, >= 45mm women), allow exceeding SVG height
            clipPath: (() => {
              const isLargeWrist = (userGender === 'men' && userWristSize >= 50) || (userGender === 'women' && userWristSize >= 45);
              return activeTab === 'Watches' && userWristSize < DEFAULT_WRIST_SIZES[userGender] && !isLargeWrist
                ? 'ellipse(220px 60px at 50% 50%)'
                : 'none';
            })(),
            overflow: 'hidden'
          }}>
            <div style={{
              position: 'relative',
              width: '100%',
              height: '100%',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center'
            }}>
              <img
                src={typeof selectedProduct === 'object' ? selectedProduct.path : selectedProduct}
                alt="Selected product"
                style={{
                  width: '100%',
                  height: '100%',
                  objectFit: 'contain',
                  transform: activeTab === 'Bracelets'
                    ? `rotate(90deg) scale(${BRACELET_HEIGHT / 30})${isRightHand ? ' scaleX(-1)' : ''}`
                    : (() => {
                        const defaultWristSize = DEFAULT_WRIST_SIZES[userGender];
                        const watchData = selectedProduct.dimensions;
                        const dialDiameter = watchData?.dialDiameter || 40; // mm
                        const totalHeight = watchData?.totalHeight || 47; // mm

                        // Realistic scaling based on dial size
                        // For 40mm dial, watch should fit naturally with height = SVG height
                        const referenceDialSize = 40; // mm - reference size for natural fit
                        const svgHeight = 300; // SVG height constraint

                        // Calculate base scale using dial size relationship
                        const dialSizeRatio = dialDiameter / referenceDialSize;
                        const wristSizeRatio = defaultWristSize / userWristSize;

                        // Base scale calculation - INVERSE relationship: larger wrist = smaller watch
                        let baseScale = (WATCH_HEIGHT / 25) * wristSizeRatio * dialSizeRatio;

                        // More realistic scaling logic
                        let enhancedScale;
                        if (userWristSize < defaultWristSize) {
                          // For smaller wrists: More moderate scaling with realistic limits
                          const sizeDifference = defaultWristSize - userWristSize;
                          const maxSizeDifference = defaultWristSize * 0.25; // Realistic scaling limit
                          const clampedDifference = Math.min(sizeDifference, maxSizeDifference);

                          // Use a gentler curve for more realistic scaling
                          const moderateScaleFactor = 1 + (clampedDifference / defaultWristSize) * 0.6;
                          enhancedScale = baseScale * moderateScaleFactor;

                          // Cap maximum scale to prevent unrealistic sizes
                          enhancedScale = Math.min(enhancedScale, baseScale * 1.25);
                        } else {
                          // For larger wrists: Linear scaling (watch appears smaller)
                          enhancedScale = baseScale;
                        }

                        // Height compensation for larger wrists (≥ default size)
                        let heightCompensation = 1;
                        const isLargeWrist = (userGender === 'men' && userWristSize >= 50) || (userGender === 'women' && userWristSize >= 45);

                        if (userWristSize >= defaultWristSize) {
                          const sizeIncrease = (userWristSize - defaultWristSize) / defaultWristSize;
                          if (isLargeWrist) {
                            // For wrist sizes >= 50mm (men) or >= 45mm (women), apply MUCH MORE height increase
                            heightCompensation = 1 + (sizeIncrease * 1.2); // 100% height increase - VERY DRAMATIC
                          } else {
                            // For smaller wrist sizes, use the original 35% scaling
                            heightCompensation = 1 + (sizeIncrease * 0.35);
                          }
                        }

                        // Apply SVG height constraint - but only for smaller wrist sizes
                        const heightConstraintScale = svgHeight / totalHeight;
                        let finalScale;

                        if (isLargeWrist) {
                          // For large wrists, allow exceeding SVG height
                          finalScale = enhancedScale;
                        } else {
                          // For smaller wrists, maintain SVG height constraint
                          finalScale = Math.min(enhancedScale, heightConstraintScale);

                          // Special case: For 40mm dial, ensure width stays same but height = SVG height
                          if (dialDiameter === referenceDialSize) {
                            finalScale = heightConstraintScale;
                          }
                        }

                        // Horizontal scaling (width adjustment)
                        let scaleX = 1;
                        if (userWristSize > defaultWristSize) {
                          scaleX = wristSizeRatio; // Compress horizontally for larger wrists
                        } else {
                          // For smaller wrists, slight width adjustment with realistic limits
                          const widthAdjustment = Math.min(1.15, 1 + (defaultWristSize - userWristSize) / defaultWristSize * 0.2);
                          scaleX = widthAdjustment;
                        }

                        // Apply height compensation
                        let finalHeightScale;
                        if (isLargeWrist) {
                          // For large wrists, allow height to exceed SVG height
                          finalHeightScale = heightCompensation;
                        } else {
                          // For smaller wrists, ensure it doesn't exceed SVG height
                          finalHeightScale = Math.min(heightCompensation, svgHeight / (totalHeight * finalScale));
                        }

                        // Center compensation: Adjust Y position to keep watch centered when height changes
                        let centerCompensation = '';
                        if (finalHeightScale !== 1) {
                          // Move up slightly when height increases to maintain center
                          const yOffset = (finalHeightScale - 1) * -10; // Negative to move up
                          centerCompensation = ` translateY(${yOffset}px)`;
                        }

                        // Safari-specific fix: Use matrix3d for better mobile compatibility
                        const isSafari = /^((?!chrome|android).)*safari/i.test(navigator.userAgent);

                        if (isSafari && isLargeWrist) {
                          // For Safari on large wrists, use matrix3d for better compatibility
                          const matrix = `matrix3d(${finalScale * scaleX}, 0, 0, 0, 0, ${finalScale * finalHeightScale}, 0, 0, 0, 0, 1, 0, 0, ${centerCompensation ? (finalHeightScale - 1) * -10 : 0}, 0, 1)`;
                          return matrix;
                        } else {
                          return `scale(${finalScale}) scaleX(${scaleX}) scaleY(${finalHeightScale})${centerCompensation}`;
                        }
                      })(),
                  // Safari-specific CSS properties for better mobile compatibility
                  WebkitTransform: (() => {
                    const defaultWristSize = DEFAULT_WRIST_SIZES[userGender];
                    const isLargeWrist = (userGender === 'men' && userWristSize >= 50) || (userGender === 'women' && userWristSize >= 45);

                    if (!isLargeWrist) return undefined;

                    const sizeIncrease = (userWristSize - defaultWristSize) / defaultWristSize;
                    const heightCompensation = 1 + (sizeIncrease * 1.0);

                    // Force Safari to use hardware acceleration with explicit transform
                    return `scale3d(1.2, ${heightCompensation}, 1) translateZ(0)`;
                  })(),
                  // Force hardware acceleration on Safari
                  WebkitBackfaceVisibility: 'hidden',
                  WebkitPerspective: 1000,
                  filter: 'drop-shadow(0 2px 8px rgba(0,0,0,0.3))'
                }}
                onLoad={(e) => removeBackground(e.target, activeTab === 'Watches' ? 'watch' : 'bracelet')}
              />
              {activeTab === 'Watches' && typeof selectedProduct === 'object' && (
                <div style={{
                  position: 'absolute',
                  bottom: '-30px',
                  left: '50%',
                  transform: 'translateX(-50%)',
                  fontSize: '11px',
                  fontWeight: '600',
                  color: 'white',
                  backgroundColor: 'rgba(45, 140, 136, 0.9)',
                  padding: '3px 8px',
                  borderRadius: '12px',
                  whiteSpace: 'nowrap',
                  pointerEvents: 'none',
                  boxShadow: '0 2px 6px rgba(0,0,0,0.3)',
                  zIndex: 2
                }}>
                  {selectedProduct.dialSize}mm
                  {userWristSize !== DEFAULT_WRIST_SIZES[userGender] && (
                    <span style={{
                      fontSize: '10px',
                      opacity: 0.8,
                      marginLeft: '4px'
                    }}>
                      {(() => {
                        const defaultWristSize = DEFAULT_WRIST_SIZES[userGender];
                        const isLargeWrist = (userGender === 'men' && userWristSize >= 50) || (userGender === 'women' && userWristSize >= 45);
                        const wristSizeRatio = defaultWristSize / userWristSize;

                        let scalingPercentage;
                        if (userWristSize < defaultWristSize) {
                          // Realistic scaling for smaller wrists
                          const sizeDifference = defaultWristSize - userWristSize;
                          const maxSizeDifference = defaultWristSize * 0.25;
                          const clampedDifference = Math.min(sizeDifference, maxSizeDifference);
                          const moderateScaleFactor = 1 + (clampedDifference / defaultWristSize) * 0.6;
                          scalingPercentage = ((moderateScaleFactor - 1) * 100).toFixed(0);
                        } else {
                          // Standard scaling for larger wrists
                          scalingPercentage = ((wristSizeRatio - 1) * 100).toFixed(0);
                        }

                        // Add debug indicator for large wrists
                        const debugSuffix = isLargeWrist ? ' 🔥' : '';
                        return `(${userWristSize < defaultWristSize ? '+' : ''}${scalingPercentage}%)${debugSuffix}`;
                      })()}
                    </span>
                  )}
                </div>
              )}
            </div>
          </div>
        )}

        {/* Camera-style Capture Button */}
        <button
          style={styles.captureBtn}
          className={isMobile ? 'mobile-capture-btn' : ''}
          onClick={handleCapture}
          aria-label={isCaptured ? "Select Products" : "Capture"}
        >
          {!isCaptured ? (
            <div style={styles.captureInner} className={isMobile ? 'mobile-inner-circle' : ''}></div>
          ) : (
            <svg width="24" height="24" viewBox="0 0 24 24" fill="white">
              <path d="M8.59 16.59L13.17 12L8.59 7.41L10 6L16 12L10 18L8.59 16.59Z"/>
            </svg>
          )}
        </button>

        {/* Reset Button - Only visible when captured */}
        {isCaptured && (
          <button
            style={styles.resetBtn}
            onClick={() => window.location.reload()}
            aria-label="Reset"
          >
            <svg width="20" height="20" viewBox="0 0 24 24" fill="white">
              <path d="M17.65 6.35C16.2 4.9 14.21 4 12 4C7.58 4 4 7.58 4 12S7.58 20 12 20C15.73 20 18.84 17.45 19.73 14H17.65C16.83 16.33 14.61 18 12 18C8.69 18 6 15.31 6 12S8.69 6 12 6C13.66 6 15.14 6.69 16.22 7.78L13 11H20V4L17.65 6.35Z"/>
            </svg>
          </button>
        )}
      </div>

      {/* Mobile Wrist Size Button - Top right corner */}
      {isCaptured && (
        <button
          style={styles.wristSizeFloatingBtn}
          className={isMobile ? 'mobile-btn' : ''}
          onClick={() => setShowWristSizeModal(true)}
          aria-label="Adjust wrist size"
        >
          <svg width="16" height="16" viewBox="0 0 24 24" fill="white">
            <path d="M12,15.5A3.5,3.5 0 0,1 8.5,12A3.5,3.5 0 0,1 12,8.5A3.5,3.5 0 0,1 15.5,12A3.5,3.5 0 0,1 12,15.5M19.43,12.97C19.47,12.65 19.5,12.33 19.5,12C19.5,11.67 19.47,11.34 19.43,11L21.54,9.37C21.73,9.22 21.78,8.95 21.66,8.73L19.66,5.27C19.54,5.05 19.27,4.96 19.05,5.05L16.56,6.05C16.04,5.66 15.5,5.32 14.87,5.07L14.5,2.42C14.46,2.18 14.25,2 14,2H10C9.75,2 9.54,2.18 9.5,2.42L9.13,5.07C8.5,5.32 7.96,5.66 7.44,6.05L4.95,5.05C4.73,4.96 4.46,5.05 4.34,5.27L2.34,8.73C2.22,8.95 2.27,9.22 2.46,9.37L4.57,11C4.53,11.34 4.5,11.67 4.5,12C4.5,12.33 4.53,12.65 4.57,12.97L2.46,14.63C2.27,14.78 2.22,15.05 2.34,15.27L4.34,18.73C4.46,18.95 4.73,19.03 4.95,18.95L7.44,17.94C7.96,18.34 8.5,18.68 9.13,18.93L9.5,21.58C9.54,21.82 9.75,22 10,22H14C14.25,22 14.46,21.82 14.5,21.58L14.87,18.93C15.5,18.68 16.04,18.34 16.56,17.94L19.05,18.95C19.27,19.03 19.54,18.95 19.66,18.73L21.66,15.27C21.78,15.05 21.73,14.78 21.54,14.63L19.43,12.97Z"/>
          </svg>
          <span style={styles.wristSizeText}>{userWristSize}mm</span>
        </button>
      )}

      {/* Wrist Size Modal - Mobile-friendly popup */}
      {showWristSizeModal && (
        <div 
          style={styles.modalOverlay} 
          onClick={() => setShowWristSizeModal(false)}
          className="modal-overlay"
        >
          <div 
            style={styles.wristSizeModal} 
            onClick={(e) => e.stopPropagation()}
            className="modal-content"
          >
            <div style={styles.modalHeader}>
              <h3 style={styles.modalTitle}>Adjust Wrist Size</h3>
              <button
                style={styles.modalCloseBtn}
                onClick={() => setShowWristSizeModal(false)}
                aria-label="Close"
              >
                <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                  <path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/>
                </svg>
              </button>
            </div>

            <div style={styles.modalContent}>
              {/* Gender Selection */}
              <div style={styles.genderSelection}>
                <button
                  style={{
                    ...styles.genderButton,
                    ...(userGender === 'men' ? styles.genderButtonActive : {})
                  }}
                  onClick={() => handleGenderChange('men')}
                >
                  Men (50mm)
                </button>
                <button
                  style={{
                    ...styles.genderButton,
                    ...(userGender === 'women' ? styles.genderButtonActive : {})
                  }}
                  onClick={() => handleGenderChange('women')}
                >
                  Women (45mm)
                </button>
              </div>

              {/* Wrist Size Slider */}
              <div style={styles.sliderContainer}>
                <label style={styles.sliderLabel}>
                  Wrist Size: {userWristSize}mm
                  {userWristSize !== DEFAULT_WRIST_SIZES[userGender] && (
                    <span style={styles.sizeChange}>
                      {(() => {
                        const defaultWristSize = DEFAULT_WRIST_SIZES[userGender];
                        const wristSizeRatio = defaultWristSize / userWristSize;

                        let scalingPercentage;
                        if (userWristSize < defaultWristSize) {
                          // Realistic scaling for smaller wrists
                          const sizeDifference = defaultWristSize - userWristSize;
                          const maxSizeDifference = defaultWristSize * 0.25;
                          const clampedDifference = Math.min(sizeDifference, maxSizeDifference);
                          const moderateScaleFactor = 1 + (clampedDifference / defaultWristSize) * 0.6;
                          scalingPercentage = ((moderateScaleFactor - 1) * 100).toFixed(0);
                        } else {
                          // Standard scaling for larger wrists
                          scalingPercentage = ((wristSizeRatio - 1) * 100).toFixed(0);
                        }

                        return `(watch ${userWristSize < defaultWristSize ? '+' : ''}${scalingPercentage}%)`;
                      })()}
                    </span>
                  )}
                </label>
                <input
                  type="range"
                  min={userGender === 'men' ? "40" : "35"}
                  max={userGender === 'men' ? "65" : "60"}
                  value={userWristSize}
                  onChange={(e) => handleWristSizeChange(parseInt(e.target.value))}
                  style={styles.slider}
                />
                <div style={styles.sliderLabels}>
                  <span>{userGender === 'men' ? "40mm" : "35mm"}</span>
                  <span>{userGender === 'men' ? "65mm" : "60mm"}</span>
                </div>

                {/* Quick Size Presets */}
                <div style={styles.presetButtons}>
                  <button
                    style={styles.presetButton}
                    onClick={() => handleWristSizeChange(userGender === 'men' ? 40 : 35)}
                  >
                    Small ({userGender === 'men' ? "40mm" : "35mm"})
                  </button>
                  <button
                    style={styles.presetButton}
                    onClick={() => handleWristSizeChange(DEFAULT_WRIST_SIZES[userGender])}
                  >
                    Average ({DEFAULT_WRIST_SIZES[userGender]}mm)
                  </button>
                  <button
                    style={styles.presetButton}
                    onClick={() => handleWristSizeChange(userGender === 'men' ? 65 : 60)}
                  >
                    Large ({userGender === 'men' ? "65mm" : "60mm"})
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Product Selection Panel */}
      {showProductSelection && (
        <div
          ref={panelRef}
          style={{
            ...styles.productSelection,
            transform: `translateY(${panelPosition}px)`,
            touchAction: 'none'
          }}
          className={isMobile ? 'mobile-product-panel' : ''}
          aria-modal="true"
          role="dialog"
          onTouchStart={handleTouchStart}
          onTouchMove={handleTouchMove}
          onTouchEnd={handleTouchEnd}
        >
          <div 
            style={styles.dragHandle} 
            aria-hidden="true"
            onTouchStart={handleTouchStart}
            onTouchMove={handleTouchMove}
            onTouchEnd={handleTouchEnd}
          />
          <div style={styles.productTabs}>
            <button
              style={{
                ...styles.tab,
                ...(activeTab === 'Watches' ? styles.activeTab : {})
              }}
              onClick={() => handleTabChange('Watches')}
            >
              Watches
            </button>
            <button
              style={{
                ...styles.tab,
                ...(activeTab === 'Bracelets' ? styles.activeTab : {})
              }}
              onClick={() => handleTabChange('Bracelets')}
            >
              Bracelets
            </button>
          </div>
          <div style={styles.productScroll} className="product-scroll">
            {getCurrentProducts().map((product, index) => {
              // Simple null check only
              if (!product) return null;

              const isSelected = (typeof selectedProduct === 'object' ? selectedProduct?.path : selectedProduct) === product.path;

              return (
                <button
                  key={index}
                  style={{
                    ...styles.productItem,
                    borderColor: isSelected ? '#2D8C88' : '#e9ecef',
                    backgroundColor: isSelected ? '#f0fffe' : '#ffffff'
                  }}
                  title={`${product.name} - ${product.caseDiameter || 'N/A'}mm`}
                  onClick={() => handleProductSelect(product)}
                  aria-label={`Select ${product.name} ${product.caseDiameter || 'N/A'}mm`}
                >
                  <img
                    src={product.path}
                    alt={product.name}
                    style={styles.productImage}
                    onError={(e) => {
                      e.target.parentElement.style.display = 'none';
                    }}
                  />
                  <div style={styles.productLabel}>
                    <div style={styles.productName}>{product.name}</div>
                    {activeTab === 'Watches' && product.caseDiameter && (
                      <div style={styles.productSize}>{product.caseDiameter}mm</div>
                    )}
                  </div>
                </button>
              );
            })}
          </div>
        </div>
      )}
    </div>
  );
};

// Styles object - Clean, modern design with improved mobile responsiveness
const styles = {
  container: {
    position: 'relative',
    height: 'calc(var(--vh, 1vh) * 100)',
    display: 'flex',
    flexDirection: 'column',
    backgroundColor: '#f8f9fa',
    color: '#333',
    fontFamily: "'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif",
    overflow: 'hidden',
    touchAction: 'manipulation',
    WebkitTapHighlightColor: 'transparent',
    WebkitOverflowScrolling: 'touch' // Smooth scrolling on iOS
  },
  cameraContainer: {
    flex: 1,
    position: 'relative',
    overflow: 'hidden',
    backgroundColor: '#000',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center'
  },
  cameraFeed: {
    width: '100%',
    height: '100%',
    objectFit: 'cover',
    transform: 'scaleX(1)' // Fix for Safari mirroring
  },
  capturedImage: {
    position: 'absolute',
    top: 0,
    left: 0,
    width: '100%',
    height: '100%',
    objectFit: 'cover',
    display: 'none',
    WebkitTransform: 'scaleX(1)' // Fix for Safari mirroring
  },

  homeBtn: {
    position: 'absolute',
    top: '20px',
    left: '20px',
    backgroundColor: 'rgba(0, 0, 0, 0.6)',
    color: 'white',
    padding: '12px',
    borderRadius: '50%',
    fontSize: '20px',
    fontWeight: '700',
    cursor: 'pointer',
    zIndex: 10,
    border: 'none',
    boxShadow: '0 2px 10px rgba(0, 0, 0, 0.3)',
    transition: 'all 0.2s ease',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    width: '44px',
    height: '44px',
    outline: 'none'
  },
  backBtn: {
    position: 'absolute',
    top: '20px',
    left: '20px',
    backgroundColor: 'rgba(0, 0, 0, 0.6)',
    color: 'white',
    padding: '12px',
    borderRadius: '50%',
    fontSize: '20px',
    fontWeight: '700',
    cursor: 'pointer',
    zIndex: 10,
    border: 'none',
    boxShadow: '0 2px 10px rgba(0, 0, 0, 0.3)',
    transition: 'all 0.2s ease',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    width: '44px',
    height: '44px',
    outline: 'none'
  },
  switchContainer: {
    position: 'absolute',
    top: '20px',
    right: '20px',
    zIndex: 10,
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'center',
    gap: '4px',
    padding: '12px',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    borderRadius: '20px',
    backdropFilter: 'blur(10px)',
    WebkitBackdropFilter: 'blur(10px)',
    boxShadow: '0 4px 12px rgba(0, 0, 0, 0.2)',
    border: '1px solid rgba(255, 255, 255, 0.1)'
  },
  switchTrack: {
    position: 'absolute',
    top: '12px',
    left: '12px',
    width: '60px',
    height: '32px',
    backgroundColor: 'rgba(0, 0, 0, 0.3)',
    borderRadius: '16px',
    border: '2px solid rgba(255, 255, 255, 0.2)',
    zIndex: 9,
    transition: 'all 0.3s ease'
  },
  switchButton: {
    position: 'relative',
    width: '28px',
    height: '28px',
    borderRadius: '50%',
    border: 'none',
    cursor: 'pointer',
    transition: 'all 0.3s ease',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    zIndex: 11,
    margin: '2px',
    boxShadow: '0 2px 8px rgba(0, 0, 0, 0.3)',
    outline: 'none',
    '&:hover': {
      transform: 'scale(1.1)'
    }
  },
  switchLabel: {
    fontSize: '12px',
    fontWeight: '700',
    color: 'white',
    textShadow: '0 1px 2px rgba(0, 0, 0, 0.7)',
    marginTop: '4px',
    padding: '4px 12px',
    borderRadius: '12px',
    backgroundColor: 'rgba(0, 0, 0, 0.3)',
    letterSpacing: '0.5px'
  },
  countdownDisplay: {
    position: 'absolute',
    top: '35%',
    left: '50%',
    transform: 'translate(-50%, -50%)',
    zIndex: 15,
    textAlign: 'center',
    pointerEvents: 'none',
    padding: '16px 24px',
    backgroundColor: 'rgba(0, 0, 0, 0.4)',
    borderRadius: '20px',
    backdropFilter: 'blur(10px)',
    WebkitBackdropFilter: 'blur(10px)',
    boxShadow: '0 4px 20px rgba(0, 0, 0, 0.3)'
  },
  countdownNumber: {
    fontSize: '72px',
    fontWeight: '900',
    color: '#2D8C88',
    textShadow: '0 2px 8px rgba(0, 0, 0, 0.5)',
    marginBottom: '8px',
    animation: 'pulse 1s ease-in-out'
  },
  countdownText: {
    fontSize: '16px',
    fontWeight: '600',
    color: 'white',
    textShadow: '0 1px 4px rgba(0, 0, 0, 0.7)'
  },
  statusMessage: {
    position: 'absolute',
    top: '25%',
    left: '50%',
    transform: 'translate(-50%, -50%)',
    zIndex: 15,
    textAlign: 'center',
    pointerEvents: 'none',
    padding: '12px 20px',
    borderRadius: '16px',
    backgroundColor: 'rgba(0, 0, 0, 0.4)',
    backdropFilter: 'blur(10px)',
    WebkitBackdropFilter: 'blur(10px)',
    boxShadow: '0 4px 20px rgba(0, 0, 0, 0.3)'
  },
  statusText: {
    fontSize: '16px',
    fontWeight: '600',
    color: 'white',
    textShadow: '0 1px 4px rgba(0, 0, 0, 0.7)',
    backgroundColor: 'rgba(255, 107, 107, 0.9)',
    padding: '12px 20px',
    borderRadius: '25px',
    marginBottom: '8px',
    transition: 'all 0.3s ease'
  },
  statusSubtext: {
    fontSize: '12px',
    fontWeight: '500',
    color: 'white',
    textShadow: '0 1px 4px rgba(0, 0, 0, 0.7)',
    backgroundColor: 'rgba(0, 0, 0, 0.6)',
    padding: '6px 12px',
    borderRadius: '15px'
  },
  handGuide: {
    position: 'absolute',
    top: '50%',
    left: '50%',
    transform: 'translate(-50%, -50%)',
    width: '80%',
    maxWidth: '400px',
    height: 'auto',
    opacity: 0.8,
    pointerEvents: 'none',
    zIndex: 5,
    filter: 'drop-shadow(0 2px 8px rgba(255, 255, 255, 0.3))',
    WebkitFilter: 'drop-shadow(0 2px 8px rgba(255, 255, 255, 0.3))',
    transition: 'all 0.3s ease'
  },
productPosition: {
  position: 'absolute',
  top: '50%',
  left: '50%',
  transform: 'translate(-50%, -50%)',
  zIndex: 8,
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
  width: '25vw', // width controlled
  aspectRatio: '1 / 1.6', // maintain height-to-width ratio (adjust as needed)
  minWidth: '100px',
  minHeight: '160px', // fallback for unsupported aspect-ratio
  pointerEvents: 'none'
}
,
  captureBtn: {
    position: 'absolute',
    bottom: '30px',
    left: '50%',
    transform: 'translateX(-50%)',
    width: '80px',
    height: '80px',
    backgroundColor: 'rgba(255, 255, 255, 0.9)',
    borderRadius: '50%',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    cursor: 'pointer',
    zIndex: 10,
    transition: 'all 0.2s ease',
    border: '4px solid rgba(255, 255, 255, 0.3)',
    boxShadow: '0 4px 20px rgba(0, 0, 0, 0.3)',
    outline: 'none',
    padding: 0,
    WebkitTapHighlightColor: 'transparent',
    touchAction: 'manipulation'
  },
  captureInner: {
    width: '60px',
    height: '60px',
    backgroundColor: '#2D8C88',
    borderRadius: '50%',
    transition: 'all 0.2s ease'
  },
  resetBtn: {
    position: 'absolute',
    bottom: '30px',
    right: '30px',
    width: '50px',
    height: '50px',
    backgroundColor: 'rgba(0, 0, 0, 0.6)',
    borderRadius: '50%',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    cursor: 'pointer',
    zIndex: 10,
    transition: 'all 0.2s ease',
    border: 'none',
    boxShadow: '0 2px 10px rgba(0, 0, 0, 0.3)',
    outline: 'none',
    padding: 0
  },

  // Wrist Size Floating Button - Follows CSS mobile patterns
  wristSizeFloatingBtn: {
    position: 'absolute',
    top: '20px',
    right: '20px',
    backgroundColor: '#2D8C88',
    color: 'white',
    padding: '12px 16px',
    borderRadius: '24px',
    fontSize: '14px',
    fontWeight: '600',
    cursor: 'pointer',
    border: 'none',
    display: 'flex',
    alignItems: 'center',
    gap: '8px',
    outline: 'none',
    transition: 'all 0.2s ease',
    zIndex: 15,
    boxShadow: '0 2px 8px rgba(45, 140, 136, 0.3)',
    minHeight: '48px',
    minWidth: '48px',
    WebkitTapHighlightColor: 'transparent',
    touchAction: 'manipulation'
  },
  wristSizeText: {
    fontSize: '12px',
    fontWeight: '600'
  },

  // Modal Styles
  modalOverlay: {
    position: 'fixed',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    zIndex: 50,
    padding: '20px',
    backdropFilter: 'blur(5px)',
    WebkitBackdropFilter: 'blur(5px)',
    touchAction: 'none'
  },
  wristSizeModal: {
    backgroundColor: 'white',
    borderRadius: '24px',
    width: '100%',
    maxWidth: '95vw',
    maxHeight: '80vh',
    overflow: 'hidden',
    boxShadow: '0 10px 30px rgba(0, 0, 0, 0.3)',
    display: 'flex',
    flexDirection: 'column',
    margin: '10px',
    position: 'relative'
  },
  modalHeader: {
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: '16px 20px 12px 20px',
    borderBottom: '1px solid #f0f0f0'
  },
  modalTitle: {
    fontSize: '18px',
    fontWeight: '700',
    color: '#2D8C88',
    margin: 0
  },
  modalCloseBtn: {
    width: '32px',
    height: '32px',
    borderRadius: '50%',
    border: 'none',
    backgroundColor: '#f5f5f5',
    color: '#666',
    cursor: 'pointer',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    transition: 'all 0.2s ease',
    outline: 'none'
  },
  modalContent: {
    padding: '16px 20px 20px 20px',
    display: 'flex',
    flexDirection: 'column',
    gap: '16px',
    overflowY: 'auto',
    WebkitOverflowScrolling: 'touch',
    maxHeight: 'calc(80vh - 60px)'
  },

  // Mobile styles handled by CSS file
  wristSizeContent: {
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'center',
    gap: '12px'
  },
  wristSizeTitle: {
    fontSize: '20px',
    fontWeight: '700',
    color: '#2D8C88',
    margin: 0,
    textAlign: 'center'
  },
  wristSizeSubtitle: {
    fontSize: '14px',
    color: '#666',
    margin: 0,
    textAlign: 'center',
    lineHeight: '1.4'
  },
  genderSelection: {
    display: 'flex',
    gap: '12px',
    width: '100%',
    maxWidth: '300px'
  },
  genderButton: {
    flex: 1,
    padding: '12px 16px',
    borderRadius: '10px',
    fontSize: '14px',
    fontWeight: '600',
    cursor: 'pointer',
    transition: 'all 0.2s ease',
    border: '2px solid #e0e0e0',
    backgroundColor: '#ffffff',
    color: '#666',
    outline: 'none'
  },
  genderButtonActive: {
    backgroundColor: '#2D8C88',
    color: '#ffffff',
    borderColor: '#2D8C88',
    boxShadow: '0 2px 8px rgba(45, 140, 136, 0.3)'
  },
  sliderContainer: {
    width: '100%',
    maxWidth: '300px',
    display: 'flex',
    flexDirection: 'column',
    gap: '8px'
  },
  sliderLabel: {
    fontSize: '16px',
    fontWeight: '600',
    color: '#333',
    textAlign: 'center',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    gap: '8px'
  },
  sizeChange: {
    fontSize: '14px',
    fontWeight: '500',
    color: '#2D8C88',
    backgroundColor: 'rgba(45, 140, 136, 0.1)',
    padding: '2px 8px',
    borderRadius: '12px'
  },
  slider: {
    width: '100%',
    height: '6px',
    borderRadius: '3px',
    background: '#e0e0e0',
    outline: 'none',
    cursor: 'pointer',
    WebkitAppearance: 'none',
    appearance: 'none'
  },
  sliderLabels: {
    display: 'flex',
    justifyContent: 'space-between',
    fontSize: '12px',
    color: '#999',
    marginTop: '4px'
  },
  presetButtons: {
    display: 'flex',
    gap: '8px',
    width: '100%',
    maxWidth: '300px'
  },
  presetButton: {
    flex: 1,
    padding: '8px 12px',
    borderRadius: '8px',
    fontSize: '12px',
    fontWeight: '500',
    cursor: 'pointer',
    transition: 'all 0.2s ease',
    border: '1px solid #e0e0e0',
    backgroundColor: '#ffffff',
    color: '#666',
    outline: 'none'
  },
  continueButton: {
    width: '100%',
    maxWidth: '300px',
    padding: '14px 24px',
    backgroundColor: '#2D8C88',
    color: '#ffffff',
    borderRadius: '12px',
    fontSize: '16px',
    fontWeight: '600',
    cursor: 'pointer',
    transition: 'all 0.2s ease',
    border: 'none',
    outline: 'none',
    boxShadow: '0 4px 12px rgba(45, 140, 136, 0.3)'
  },

  productSelection: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: 'rgba(255, 255, 255, 0.98)',
    backdropFilter: 'blur(20px)',
    WebkitBackdropFilter: 'blur(20px)',
    borderTopLeftRadius: '24px',
    borderTopRightRadius: '24px',
    padding: '16px',
    maxHeight: '85vh',
    display: 'flex',
    flexDirection: 'column',
    zIndex: 20,
    boxShadow: '0 -4px 20px rgba(0, 0, 0, 0.15)',
    border: 'none',
    transform: 'translateY(0)',
    transition: 'transform 0.3s ease-out',
    overflow: 'hidden',
    touchAction: 'none',
    willChange: 'transform',
    userSelect: 'none',
    WebkitUserSelect: 'none'
  },
  closeBtn: {
    position: 'absolute',
    top: '12px',
    right: '16px',
    color: '#666',
    cursor: 'pointer',
    zIndex: 21,
    width: '32px',
    height: '32px',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: '50%',
    backgroundColor: 'rgba(0, 0, 0, 0.1)',
    transition: 'all 0.2s ease',
    border: 'none',
    outline: 'none',
    padding: 0
  },
  productTabs: {
    display: 'flex',
    marginBottom: '16px',
    backgroundColor: '#f0f0f0',
    borderRadius: '12px',
    padding: '4px',
    gap: '4px'
  },
  tab: {
    flex: 1,
    textAlign: 'center',
    padding: '12px 16px',
    borderRadius: '8px',
    fontSize: '16px',
    fontWeight: '600',
    cursor: 'pointer',
    transition: 'all 0.2s ease',
    color: '#666',
    outline: 'none',
    border: 'none',
    backgroundColor: 'transparent',
    WebkitTapHighlightColor: 'transparent',
    touchAction: 'manipulation'
  },
  activeTab: {
    backgroundColor: '#2D8C88',
    color: '#ffffff',
    boxShadow: '0 1px 4px rgba(45, 140, 136, 0.3)'
  },
  productScroll: {
    display: 'grid',
    gridTemplateColumns: 'repeat(auto-fill, minmax(100px, 1fr))',
    gap: '12px',
    maxHeight: 'calc(85vh - 120px)',
    overflowY: 'auto',
    paddingBottom: '16px',
    scrollbarWidth: 'thin',
    scrollbarColor: '#ddd #f5f5f5',
    WebkitOverflowScrolling: 'touch'
  },
  productItem: {
    position: 'relative',
    width: '100%',
    aspectRatio: '1/1',
    backgroundColor: '#ffffff',
    borderRadius: '16px',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    cursor: 'pointer',
    transition: 'all 0.2s ease',
    border: '1px solid #e0e0e0',
    overflow: 'hidden',
    boxShadow: '0 2px 8px rgba(0, 0, 0, 0.08)',
    padding: '8px',
    outline: 'none',
    WebkitTapHighlightColor: 'transparent',
    touchAction: 'manipulation'
  },
  productImage: {
    width: '100%',
    height: '100%',
    objectFit: 'contain',
    borderRadius: '8px',
    backgroundColor: '#ffffff'
  },
  productLabel: {
    position: 'absolute',
    bottom: '3px',
    left: '3px',
    right: '3px',
    fontSize: '9px',
    color: '#666',
    textAlign: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.95)',
    borderRadius: '3px',
    padding: '3px 2px',
    overflow: 'hidden'
  },
  productName: {
    fontSize: '9px',
    fontWeight: '600',
    whiteSpace: 'nowrap',
    textOverflow: 'ellipsis',
    overflow: 'hidden',
    marginBottom: '1px'
  },
  productSize: {
    fontSize: '8px',
    fontWeight: '500',
    color: '#2D8C88',
    whiteSpace: 'nowrap'
  },
  dragHandle: {
    width: '40px',
    height: '4px',
    backgroundColor: '#e0e0e0',
    borderRadius: '2px',
    margin: '0 auto 12px',
    cursor: 'grab',
    touchAction: 'none',
    userSelect: 'none',
    WebkitUserSelect: 'none'
  },

  desktopContainer: {
    position: 'relative',
    height: '100vh',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#f8f9fa',
    padding: '20px'
  },
  qrContainer: {
    backgroundColor: 'white',
    padding: '40px',
    borderRadius: '24px',
    boxShadow: '0 10px 30px rgba(0, 0, 0, 0.1)',
    textAlign: 'center',
    maxWidth: '500px',
    width: '100%'
  },
  qrTitle: {
    fontSize: '28px',
    fontWeight: '700',
    color: '#2D8C88',
    marginBottom: '16px'
  },
  qrSubtitle: {
    fontSize: '16px',
    color: '#666',
    marginBottom: '32px',
    lineHeight: '1.5'
  },
  qrWrapper: {
    backgroundColor: 'white',
    padding: '20px',
    borderRadius: '16px',
    display: 'inline-block',
    marginBottom: '24px',
    boxShadow: '0 4px 12px rgba(0, 0, 0, 0.05)'
  },
  qrLink: {
    fontSize: '14px',
    color: '#2D8C88',
    marginBottom: '32px',
    wordBreak: 'break-all'
  }
};

export default Tryon;